!classDefinition: #TestBookWise category: #'2025-1C-Recuperatorio-1'!
TestCase subclass: #TestBookWise
	instanceVariableNames: 'usuarioEstudiante usuarioProfesor usuarioBibliotecario libroRegular unLibroRegular dateProvider unLibroNuevo unLibroDeReferencia'
	classVariableNames: ''
	poolDictionaries: ''
	category: '2025-1C-Recuperatorio-1'!

!TestBookWise methodsFor: 'setUp' stamp: 'AG 7/1/2025 21:58:54'!
setUp
	usuarioEstudiante := Usuario estudiante .
	usuarioProfesor := Usuario profesor .
	usuarioBibliotecario := Usuario bibliotecario .
	unLibroRegular := Libro regular .
	unLibroNuevo := Libro nuevo.
	unLibroDeReferencia := Libro deReferencia .
	dateProvider := DateProvider conFecha: July/1/2025.! !


!TestBookWise methodsFor: 'tests' stamp: 'AG 7/1/2025 21:23:02'!
test01UnUsuarioPuedePedirPrestadoUnLibro

	|  unSistema |
	unSistema := self sistemaDePrueba.
	
	
	unSistema pedirPrestado: unLibroRegular por: usuarioProfesor .
	
	self assert: (unSistema elUsuario: usuarioProfesor tieneLibroPrestado: unLibroRegular ).! !

!TestBookWise methodsFor: 'tests' stamp: 'AG 7/1/2025 21:52:22'!
test02EstudiantePuedeTomarUnLibroSimultaneamente

	| limiteDePrestamosParaEstudiante |
	
	limiteDePrestamosParaEstudiante := 1.
	self assertQue: usuarioEstudiante llegaASuLimiteAlPedirPrestado: limiteDePrestamosParaEstudiante.! !

!TestBookWise methodsFor: 'tests' stamp: 'AG 7/1/2025 17:37:42'!
test03ProfesorPuedeTomarPrestado4LibrosSimultaneamente
	
	| limiteDePrestamosParaProfesor |
	
	limiteDePrestamosParaProfesor := 4.
	self assertQue: usuarioProfesor llegaASuLimiteAlPedirPrestado: limiteDePrestamosParaProfesor.! !

!TestBookWise methodsFor: 'tests' stamp: 'AG 7/1/2025 17:41:15'!
test04BibliotecarioPuedeTomarPrestado2LibrosSimultaneamente
	
	| limiteDePrestamosParaBibliotecario |
	
	limiteDePrestamosParaBibliotecario := 2.
	self assertQue: usuarioBibliotecario llegaASuLimiteAlPedirPrestado: limiteDePrestamosParaBibliotecario.! !

!TestBookWise methodsFor: 'tests' stamp: 'AG 7/1/2025 19:56:13'!
test05UsuarioDevuelveLibro
 
	|  unSistema  usuario |
	unSistema := self sistemaDePrueba.
	usuario := Usuario profesor .
	
	

	unSistema pedirPrestado: unLibroRegular por: usuario.
 
    self assert: (usuario cantidadDePrestamosActivos = 1).
	unSistema devolverPrestamo: unLibroRegular por: usuario.

    
    self assert: usuario prestamosActivos isEmpty.! !

!TestBookWise methodsFor: 'tests' stamp: 'AG 7/1/2025 20:22:37'!
test06EstudianteConLibroRegularPaga50PorcientoDeLaMultaLuegoDe2DiasDeAtraso
 	
	|  unSistema  usuario multaEsperada multaPagada |
	unSistema := self sistemaDePrueba.
	usuario := Usuario estudiante .
	unSistema pedirPrestado: unLibroRegular por: usuario.
	
	dateProvider avanzar16Dias .
    multaPagada := unSistema devolverPrestamo: unLibroRegular por: usuario .
	multaEsperada := 5 *peso.
    self assert: multaEsperada equals: multaPagada .! !

!TestBookWise methodsFor: 'tests' stamp: 'AG 7/1/2025 20:23:24'!
test07ProfesorConLibroRegularPaga100PorcientoDeLaMultaLuegoDe2DiasDeAtraso
 	
	|  unSistema  usuario multaEsperada multaPagada |
	unSistema := self sistemaDePrueba.
	usuario := Usuario profesor .
	unSistema pedirPrestado: unLibroRegular por: usuario .
 
    
	dateProvider avanzar16Dias .
    multaPagada :=  unSistema devolverPrestamo: unLibroRegular por: usuario .
	multaEsperada := 10 *peso.
    self assert: multaEsperada equals: multaPagada .! !

!TestBookWise methodsFor: 'tests' stamp: 'AG 7/1/2025 20:23:27'!
test08BibliotecarioConLibroRegularPaga25PorcientoDeLaMultaLuegoDe2DiasDeAtraso
 	
	|  unSistema  usuario multaEsperada multaPagada |
	unSistema := self sistemaDePrueba.
	usuario := Usuario bibliotecario .
	unSistema pedirPrestado: unLibroRegular por: usuario .
 
    dateProvider avanzar16Dias .

    multaPagada := unSistema devolverPrestamo: unLibroRegular por: usuario .
	multaEsperada := 2.5*peso. "la multa es de 10 por dos dias entonces 10*0.25"
    self assert: multaEsperada equals: multaPagada .! !

!TestBookWise methodsFor: 'tests' stamp: 'AG 7/1/2025 21:56:16'!
test09EstudianteTiene2DiasDeAtrasoParaLibro
	|  unSistema    diasDeAtrasoEsperados diasDeAtrasoObtenidos  |
	unSistema := self sistemaDePrueba.
	unSistema pedirPrestado: unLibroRegular por: usuarioBibliotecario .
 
    dateProvider avanzar16Dias .

    diasDeAtrasoEsperados := 2 * day. 
	diasDeAtrasoObtenidos := unSistema elUsuario: usuarioBibliotecario diasDeAtrasoPara: unLibroRegular .
	self assert: diasDeAtrasoEsperados equals: diasDeAtrasoObtenidos .
	! !

!TestBookWise methodsFor: 'tests' stamp: 'AG 7/1/2025 22:04:28'!
test10BibliotecarioConLibroRegularYNuevoTieneTotalDe13PesosDeMulta
	|  unSistema  multaTotalEsperada multaTotal multaLibroNuevo multaLibroRegular |
	unSistema := self sistemaDePrueba.

	unSistema pedirPrestado: unLibroRegular por: usuarioBibliotecario .
	unSistema pedirPrestado: unLibroNuevo por: usuarioBibliotecario .
	
	dateProvider avanzar16Dias .
	"Para libroRegular: 16 - 14 = 2 días atraso, multa $5/día * 25% (bibliotecario)"
	multaLibroRegular := 2 * (5 * 0.25).

	"Para libroNuevo: 16 - 10 = 6 días atraso, multa $8/día * 25%"
	multaLibroNuevo := 6* (8 * 0.25).
	multaTotalEsperada := (multaLibroRegular + multaLibroNuevo) * peso. 
	multaTotal := unSistema cantidadDeMultaselUsuario: usuarioBibliotecario .
	self assert: multaTotalEsperada equals: multaTotal .

	! !

!TestBookWise methodsFor: 'tests' stamp: 'AG 7/1/2025 22:04:00'!
test11BibliotecarioConLibroNuevoYDeReferenciaTieneTotalDeMultas
	|  unSistema    multaTotalEsperada multaTotal multaLibroNuevo multaLibroDeReferencia |
	unSistema := self sistemaDePrueba.

	unSistema pedirPrestado: unLibroDeReferencia por: usuarioBibliotecario .
	unSistema pedirPrestado: unLibroNuevo por: usuarioBibliotecario .
	
	dateProvider avanzar16Dias .
	"Para libroDeReferencia: 16 - 7 = 9 días atraso, multa $10/día * 25% (bibliotecario)"
	multaLibroDeReferencia := 9 * (10 * 0.25).

	"Para libroNuevo: 16 - 10 = 6 días atraso, multa $8/día * 25%"
	multaLibroNuevo := 6* (8 * 0.25).
	multaTotalEsperada := (multaLibroDeReferencia + multaLibroNuevo) * peso. 
	multaTotal := unSistema cantidadDeMultaselUsuario: usuarioBibliotecario .
	self assert: multaTotalEsperada equals: multaTotal .

	! !


!TestBookWise methodsFor: 'assertions' stamp: 'AG 7/1/2025 21:51:50'!
assertQue: unUsuario llegaASuLimiteAlPedirPrestado: unaCantidadDeVeces
	| unSistema unLibro |
	unSistema := self sistemaDePrueba.
	unLibro := unLibroRegular .
	
	unaCantidadDeVeces timesRepeat: [unSistema pedirPrestado: unLibro por: unUsuario ].
	
	self should:[unSistema pedirPrestado: unLibro por: unUsuario .]
		raise: Error
		withExceptionDo: [: anError| self assert: anError messageText equals: unUsuario cantidadMaximaDePrestamoAlcanzadaErrorDescription]! !


!TestBookWise methodsFor: 'test objects' stamp: 'AG 7/1/2025 20:10:27'!
sistemaDePrueba

	^SistemaBookWise conDateProvider: dateProvider.
! !

!TestBookWise methodsFor: 'test objects' stamp: 'LL 6/29/2025 12:56:19'!
un2doLibroCualquiera

	^ self un2doLibroRegular! !

!TestBookWise methodsFor: 'test objects' stamp: 'LL 6/29/2025 23:32:57'!
un2doLibroRegular

	^'TDD by Example'! !

!TestBookWise methodsFor: 'test objects' stamp: 'LL 6/29/2025 23:33:02'!
un3erLibroCualquiera

	^'Object Thinking'! !

!TestBookWise methodsFor: 'test objects' stamp: 'LL 6/29/2025 23:33:06'!
un4toLibroCualquiera

	^'Smalltalk, Objects and Design'! !

!TestBookWise methodsFor: 'test objects' stamp: 'LL 6/29/2025 23:33:10'!
un5toLibroCualquiera

	^'Planning Extreme Programming'! !


!classDefinition: #DateProvider category: #'2025-1C-Recuperatorio-1'!
Object subclass: #DateProvider
	instanceVariableNames: 'date'
	classVariableNames: ''
	poolDictionaries: ''
	category: '2025-1C-Recuperatorio-1'!

!DateProvider methodsFor: 'as yet unclassified' stamp: 'AG 7/1/2025 20:22:06'!
avanzar16Dias
	date :=   date next: 16*day.! !

!DateProvider methodsFor: 'as yet unclassified' stamp: 'AG 7/1/2025 19:54:57'!
initializeConFecha: unaFecha
	date := unaFecha .! !

!DateProvider methodsFor: 'as yet unclassified' stamp: 'AG 7/1/2025 19:58:53'!
today
	^date! !

"-- -- -- -- -- -- -- -- -- -- -- -- -- -- -- -- -- -- "!

!classDefinition: 'DateProvider class' category: #'2025-1C-Recuperatorio-1'!
DateProvider class
	instanceVariableNames: ''!

!DateProvider class methodsFor: 'as yet unclassified' stamp: 'AG 7/1/2025 19:54:31'!
conFecha: unaFecha
	^self new initializeConFecha: unaFecha .! !


!classDefinition: #Libro category: #'2025-1C-Recuperatorio-1'!
Object subclass: #Libro
	instanceVariableNames: 'plazoDePrestamo multaPorDia'
	classVariableNames: ''
	poolDictionaries: ''
	category: '2025-1C-Recuperatorio-1'!

!Libro methodsFor: 'private' stamp: 'AG 7/1/2025 18:39:13'!
multaPorDia
	^multaPorDia ! !

!Libro methodsFor: 'private' stamp: 'AG 7/1/2025 18:44:49'!
plazoDePrestamo
	^plazoDePrestamo ! !


!Libro methodsFor: 'initialization' stamp: 'AG 7/1/2025 19:16:43'!
initializeConPlazoDePrestamo: unPlazo yMultaPorDia: unaMulta
	 plazoDePrestamo := unPlazo .
	multaPorDia := unaMulta .! !

"-- -- -- -- -- -- -- -- -- -- -- -- -- -- -- -- -- -- "!

!classDefinition: 'Libro class' category: #'2025-1C-Recuperatorio-1'!
Libro class
	instanceVariableNames: ''!

!Libro class methodsFor: 'as yet unclassified' stamp: 'AG 7/1/2025 19:17:39'!
deReferencia
	^self new initializeConPlazoDePrestamo: 7 *day yMultaPorDia: 10 *peso/day.
! !

!Libro class methodsFor: 'as yet unclassified' stamp: 'AG 7/1/2025 19:17:59'!
nuevo
	^self new initializeConPlazoDePrestamo: 10 *day yMultaPorDia: 8*peso/day.
! !

!Libro class methodsFor: 'as yet unclassified' stamp: 'AG 7/1/2025 19:15:59'!
regular
	^self new initializeConPlazoDePrestamo: 14 *day yMultaPorDia: 5 *peso/day.
! !


!classDefinition: #Prestamo category: #'2025-1C-Recuperatorio-1'!
Object subclass: #Prestamo
	instanceVariableNames: 'libroPrestado usuario fechaDePrestamo'
	classVariableNames: ''
	poolDictionaries: ''
	category: '2025-1C-Recuperatorio-1'!

!Prestamo methodsFor: 'initialization' stamp: 'AG 7/1/2025 17:57:17'!
initializeCon: unLibro yUnUsuario: unUsuario enFecha: unaFecha
	libroPrestado := unLibro .
	usuario := unUsuario .
	fechaDePrestamo := unaFecha .! !


!Prestamo methodsFor: 'private' stamp: 'AG 7/1/2025 22:10:15'!
calcularMultaPara: unUsuario aFecha: unaFecha
    ^ (self diasDeAtrasoAFecha: unaFecha) * libroPrestado multaPorDia * unUsuario porcentajeMulta! !

!Prestamo methodsFor: 'private' stamp: 'AG 7/1/2025 20:33:53'!
diasDeAtrasoAFecha: unaFecha
	 | diasTranscurridos |
    diasTranscurridos := fechaDePrestamo distanceTo: unaFecha .
    ^ (diasTranscurridos - libroPrestado plazoDePrestamo) max: 0! !

!Prestamo methodsFor: 'private' stamp: 'AG 7/1/2025 21:17:25'!
estaVencido: unaFecha
	^ (self diasDeAtrasoAFecha: (unaFecha )) > 0
	! !

!Prestamo methodsFor: 'private' stamp: 'AG 7/1/2025 18:03:35'!
libroPrestado
	^libroPrestado ! !

"-- -- -- -- -- -- -- -- -- -- -- -- -- -- -- -- -- -- "!

!classDefinition: 'Prestamo class' category: #'2025-1C-Recuperatorio-1'!
Prestamo class
	instanceVariableNames: ''!

!Prestamo class methodsFor: 'as yet unclassified' stamp: 'AG 7/1/2025 17:56:15'!
nuevoCon: unLibro usuario: unUsuario fecha: unaFecha
	^self new initializeCon: unLibro yUnUsuario: unUsuario enFecha: unaFecha.! !


!classDefinition: #SistemaBookWise category: #'2025-1C-Recuperatorio-1'!
Object subclass: #SistemaBookWise
	instanceVariableNames: 'prestamosPorUsuario date'
	classVariableNames: ''
	poolDictionaries: ''
	category: '2025-1C-Recuperatorio-1'!

!SistemaBookWise methodsFor: 'initialization' stamp: 'AG 7/1/2025 20:01:46'!
initializeConDateProvider: unDateProvider 
	date := unDateProvider .
	prestamosPorUsuario := Dictionary new.! !


!SistemaBookWise methodsFor: 'prestamos' stamp: 'AG 7/1/2025 20:02:34'!
devolverPrestamo: unLibro por: unUsuario 
	 | multaAPagar |
	multaAPagar := unUsuario devolver: unLibro conFechaDeDevolucion: date today.
	prestamosPorUsuario removeKey: unUsuario.
	^multaAPagar ! !

!SistemaBookWise methodsFor: 'prestamos' stamp: 'AG 7/1/2025 21:53:48'!
pedirPrestado: unLibro por: unUsuario 

	| prestamosAUsuario unPrestamo |
	unPrestamo := Prestamo nuevoCon: unLibro usuario: self fecha: date today.
	unUsuario tomarPrestado: unPrestamo .
	prestamosAUsuario := prestamosPorUsuario at: unUsuario ifAbsent: [ OrderedCollection new ].
	prestamosAUsuario add: unPrestamo.
	prestamosPorUsuario at: unUsuario put: prestamosAUsuario.! !


!SistemaBookWise methodsFor: 'testing' stamp: 'AG 7/1/2025 21:17:19'!
cantidadDeMultaselUsuario: unUsuario 
	^unUsuario calcularMultaTotalAFecha: date today
	! !

!SistemaBookWise methodsFor: 'testing' stamp: 'AG 7/1/2025 21:17:54'!
elUsuario: unUsuario diasDeAtrasoPara: unLibro
	| prestamosDelUsuario prestamo |
	prestamosDelUsuario := prestamosPorUsuario at: unUsuario ifAbsent: [ ^ 0 ].

	prestamo := prestamosDelUsuario
		detect: [:p | p libroPrestado = unLibro and: [ p estaVencido: date today] ]
		ifNone: [ ^ 0 ].

	^ prestamo diasDeAtrasoAFecha: (date today)
	! !

!SistemaBookWise methodsFor: 'testing' stamp: 'AG 7/1/2025 21:24:21'!
elUsuario: unUsuario tieneLibroPrestado: unLibro

	| prestamosDelUsuario |
	prestamosDelUsuario := prestamosPorUsuario at: unUsuario ifAbsent: [ ^ false ].
	^ prestamosDelUsuario anySatisfy: [:p | p libroPrestado = unLibro ].! !

"-- -- -- -- -- -- -- -- -- -- -- -- -- -- -- -- -- -- "!

!classDefinition: 'SistemaBookWise class' category: #'2025-1C-Recuperatorio-1'!
SistemaBookWise class
	instanceVariableNames: ''!

!SistemaBookWise class methodsFor: 'initialization' stamp: 'AG 7/1/2025 20:01:46'!
conDateProvider: unDateProvider
	^self new initializeConDateProvider: unDateProvider ! !


!classDefinition: #Usuario category: #'2025-1C-Recuperatorio-1'!
Object subclass: #Usuario
	instanceVariableNames: 'limiteDePrestamos prestamos porcentajeDeMulta'
	classVariableNames: ''
	poolDictionaries: ''
	category: '2025-1C-Recuperatorio-1'!

!Usuario methodsFor: 'exceptions' stamp: 'AG 7/1/2025 17:19:25'!
cantidadMaximaDePrestamoAlcanzadaErrorDescription
	^'Cantidad maxima de libros prestados alcanzada'! !

!Usuario methodsFor: 'exceptions' stamp: 'AG 7/1/2025 18:11:23'!
libroNoTomadoComoPrestamoErrorDescription
	^'El libro no se encuentra dentro de los prestamos'! !

!Usuario methodsFor: 'exceptions' stamp: 'AG 7/1/2025 17:28:38'!
signalCantidadMaximaDePrestamosAlcanzadaError
	self error: self cantidadMaximaDePrestamoAlcanzadaErrorDescription .! !

!Usuario methodsFor: 'exceptions' stamp: 'AG 7/1/2025 18:12:04'!
signalLibroNoEncontradoComoPrestamoError
	self error: self libroNoTomadoComoPrestamoErrorDescription .! !


!Usuario methodsFor: 'private' stamp: 'AG 7/1/2025 22:10:15'!
calcularMultaTotalAFecha: unaFecha
	| total |
	total := 0.
    ( prestamos select: [:p | p estaVencido: unaFecha ]) do: [ :p | 
		| multaPrestamo | 
		multaPrestamo := (p calcularMultaPara:  self aFecha: unaFecha).
		total := total + multaPrestamo.
	].
	^total! !

!Usuario methodsFor: 'private' stamp: 'AG 7/1/2025 17:59:26'!
cantidadDePrestamosActivos
	^prestamos size
	! !

!Usuario methodsFor: 'private' stamp: 'AG 7/1/2025 22:10:15'!
devolver: unLibro conFechaDeDevolucion: unaFecha
    | prestamo multa |
    prestamo := prestamos 
        detect: [:p | p libroPrestado = unLibro ]
        ifNone: [ self signalLibroNoEncontradoComoPrestamoError ].
	
	multa := prestamo calcularMultaPara: self aFecha: unaFecha .
    prestamos remove: prestamo.
   ^multa ! !

!Usuario methodsFor: 'private' stamp: 'AG 7/1/2025 19:01:18'!
porcentajeMulta
	^porcentajeDeMulta ! !

!Usuario methodsFor: 'private' stamp: 'AG 7/1/2025 18:08:40'!
prestamosActivos
	^prestamos ! !

!Usuario methodsFor: 'private' stamp: 'AG 7/1/2025 19:31:35'!
prestamosVencidos
	  ^ self prestamosActivos select: [:p | p estaVencido ].! !

!Usuario methodsFor: 'private' stamp: 'AG 7/1/2025 18:01:01'!
puedoTomarPrestado
	( limiteDePrestamos = self cantidadDePrestamosActivos ) ifTrue: [^ self signalCantidadMaximaDePrestamosAlcanzadaError ]! !

!Usuario methodsFor: 'private' stamp: 'AG 7/1/2025 21:53:32'!
tomarPrestado: unPrestamo
	self puedoTomarPrestado.
	prestamos add: unPrestamo .! !


!Usuario methodsFor: 'initialization' stamp: 'AG 7/1/2025 19:12:16'!
initializeConCantidadDePrestamosMaxima:  unaCantidadMaxima yPorcentajePorMulta: unPorcentaje
	 limiteDePrestamos := unaCantidadMaxima.
	prestamos := OrderedCollection new.
	porcentajeDeMulta := unPorcentaje.! !

"-- -- -- -- -- -- -- -- -- -- -- -- -- -- -- -- -- -- "!

!classDefinition: 'Usuario class' category: #'2025-1C-Recuperatorio-1'!
Usuario class
	instanceVariableNames: ''!

!Usuario class methodsFor: 'initialization' stamp: 'AG 7/1/2025 19:11:15'!
bibliotecario
	^self new initializeConCantidadDePrestamosMaxima:  2 yPorcentajePorMulta: 0.25.! !

!Usuario class methodsFor: 'initialization' stamp: 'AG 7/1/2025 19:13:16'!
estudiante
	^self new initializeConCantidadDePrestamosMaxima:  1 yPorcentajePorMulta: 0.5.! !

!Usuario class methodsFor: 'initialization' stamp: 'AG 7/1/2025 19:13:29'!
profesor
	^self new initializeConCantidadDePrestamosMaxima:  4 yPorcentajePorMulta: 1.

! !
